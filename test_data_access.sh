#!/bin/bash

# Simple test script to verify data access from WSL
echo "🔍 Testing data access from WSL..."

echo "📁 Checking a:/data directory via Docker:"
if docker run --rm -v "a:/data:/data" alpine:latest test -d /data; then
    echo "✅ Directory exists"
    echo "📄 Files in directory:"
    docker run --rm -v "a:/data:/data" alpine:latest ls -la /data/
else
    echo "❌ Directory a:/data does not exist or is not accessible"
    exit 1
fi

echo ""
echo "🎯 Checking usa_exchanges.csv:"
if docker run --rm -v "a:/data:/data" alpine:latest test -f /data/usa_exchanges.csv; then
    echo "✅ File exists"
    echo "📊 File info:"
    docker run --rm -v "a:/data:/data" alpine:latest wc -l /data/usa_exchanges.csv
    echo "📝 First few lines:"
    docker run --rm -v "a:/data:/data" alpine:latest head -3 /data/usa_exchanges.csv
else
    echo "❌ File a:/data/usa_exchanges.csv does not exist"
    exit 1
fi

echo ""
echo "🐳 Testing Docker volume mount:"
docker run --rm -v "a:/data:/data" alpine:latest ls -la /data/

echo ""
echo "✅ All tests passed! Data should be accessible from Docker containers."
