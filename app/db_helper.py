import csv
import os

from crud import SECDBManager


def get_db_manager() -> SECDBManager:
    """Get database manager with connection string from environment or default."""
    db_url = os.getenv('DATABASE_URL', '***********************************************/myapp')
    return SECDBManager(db_url)

def read_csv(csv_file_path: str) -> list[dict]:
    """Read CSV file and return list of rows."""
    if not os.path.exists(csv_file_path):
        raise FileNotFoundError(f"CSV file not found: {csv_file_path}")
    with open(csv_file_path, newline='', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        return [row for row in reader]

def load_exchanges(csv_file_path: str | None = None) -> bool:
    """
    Load exchanges from CSV file into database.

    Args:
        csv_file_path: Path to CSV file. If None, tries default path then creates sample data.

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        print(f"Attempting to load exchanges from: {csv_file_path}")

        # Check if file exists
        if csv_file_path and os.path.exists(csv_file_path):
            print(f"File found: {csv_file_path}")
        else:
            print(f"File not found: {csv_file_path}")
            return False

        with get_db_manager() as db:
            print("Database connection established")
            rows = read_csv(csv_file_path)
            for row in rows:
                # Check if exchange already exists
                existing = db.get_exchange(row['exchange_code'])
                if not existing:
                    success = db.add_exchange(
                        row['exchange_code'],
                        row['exchange_name'],
                        row['country']
                    )
                    if success:
                        print(f"Added exchange: {row['exchange_code']} - {row['exchange_name']}")
                    else:
                        print(f"Failed to add exchange: {row['exchange_code']}")
                else:
                    print(f"Exchange already exists: {row['exchange_code']}")
            return True

    except Exception as e:
        print(f"Error loading exchanges: {e}")
        import traceback
        traceback.print_exc()
        return False

def load_sic_codes(csv_file_path: str | None = None) -> bool:
    """
    Load SIC codes from CSV file into database.

    Args:
        csv_file_path: Path to CSV file. If None, tries default path then creates sample data.

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        print(f"Attempting to load SIC codes from: {csv_file_path}")

        # Check if file exists
        if csv_file_path and os.path.exists(csv_file_path):
            print(f"File found: {csv_file_path}")
        else:
            print(f"File not found: {csv_file_path}")
            return False

        with get_db_manager() as db:
            print("Database connection established")
            rows = read_csv(csv_file_path)
            for row in rows:
                # Check if SIC code already exists
                existing = db.get_sic_code(row['sic_code'])
                if not existing:
                    success = db.add_sic_code(
                        row['sic_code'],
                        row['office_title'],
                        row['industry_title'],
                        row['division']
                    )
                    if success:
                        print(f"Added SIC code: {row['sic_code']} - {row['industry_title']}")
                    else:
                        print(f"Failed to add SIC code: {row['sic_code']}")
                else:
                    print(f"SIC code already exists: {row['sic_code']}")
            return True

    except Exception as e:
        print(f"Error loading SIC codes: {e}")
        import traceback
        traceback.print_exc()
        return False
