if __name__ == "__main__":
    import argparse
    import os
    from db_helper import load_exchanges, load_sic_codes, load_companies

    parser = argparse.ArgumentParser()
    parser.add_argument("--load", choices=["exchanges", "sic_codes", "companies", "all"], help="Load data from CSV")
    args = parser.parse_args()

    # Data files are always in /data directory (mapped from a:/data)
    exchanges_file = "/data/usa_exchanges.csv"
    sic_codes_file = "/data/sic_codes.csv"
    companies_file = "/data/listed_filer_metadata.csv"

    if args.load == "exchanges":
        print(f"Loading exchanges from: {exchanges_file}")
        if os.path.exists(exchanges_file):
            success = load_exchanges(exchanges_file)
        else:
            print(f"Exchanges file not found: {exchanges_file}")

    elif args.load == "sic_codes":
        print(f"Loading SIC codes from: {sic_codes_file}")
        if os.path.exists(sic_codes_file):
            success = load_sic_codes(sic_codes_file)
        else:
            print(f"SIC codes file not found: {sic_codes_file}")

    elif args.load == "companies":
        print(f"Loading companies from: {companies_file}")
        if os.path.exists(companies_file):
            success = load_companies(companies_file)
        else:
            print(f"Companies file not found: {companies_file}")

    elif args.load == "all":
        print("Loading all data from /data directory...")

        # Load exchanges
        if os.path.exists(exchanges_file):
            print("Loading exchanges...")
            success_exchanges = load_exchanges(exchanges_file)
        else:
            print(f"Exchanges file not found: {exchanges_file}")
            success_exchanges = False

        # Load SIC codes
        if os.path.exists(sic_codes_file):
            print("Loading SIC codes...")
            success_sic = load_sic_codes(sic_codes_file)
        else:
            print(f"SIC codes file not found: {sic_codes_file}")
            success_sic = False

        # Load companies
        if os.path.exists(companies_file):
            print("Loading companies...")
            success_companies = load_companies(companies_file)
        else:
            print(f"Companies file not found: {companies_file}")
            success_companies = False

        success = success_exchanges and success_sic and success_companies
