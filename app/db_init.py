if __name__ == "__main__":
    import argparse
    import os
    from db_helper import load_exchanges, load_sic_codes

    parser = argparse.ArgumentParser()
    parser.add_argument("--load", choices=["exchanges", "sic_codes", "all"], help="Load data from CSV")
    args = parser.parse_args()

    # Data files are always in /data directory (mapped from a:/data)
    exchanges_file = "/data/usa_exchanges.csv"
    sic_codes_file = "/data/sic_codes.csv"

    if args.load == "exchanges":
        print(f"Loading exchanges from: {exchanges_file}")
        if os.path.exists(exchanges_file):
            success = load_exchanges(exchanges_file)
        else:
            print(f"Exchanges file not found: {exchanges_file}")

    elif args.load == "sic_codes":
        print(f"Loading SIC codes from: {sic_codes_file}")
        if os.path.exists(sic_codes_file):
            success = load_sic_codes(sic_codes_file)
        else:
            print(f"SIC codes file not found: {sic_codes_file}")

    elif args.load == "all":
        print("Loading all data from /data directory...")

        # Load exchanges
        if os.path.exists(exchanges_file):
            print("Loading exchanges...")
            success_exchanges = load_exchanges(exchanges_file)
        else:
            print(f"Exchanges file not found: {exchanges_file}")
            success_exchanges = False

        # Load SIC codes
        if os.path.exists(sic_codes_file):
            print("Loading SIC codes...")
            success_sic = load_sic_codes(sic_codes_file)
        else:
            print(f"SIC codes file not found: {sic_codes_file}")
            success_sic = False

        success = success_exchanges and success_sic
