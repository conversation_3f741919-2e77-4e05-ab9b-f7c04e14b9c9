if __name__ == "__main__":
    import argparse
    import os
    from db_helper import load_exchanges

    parser = argparse.ArgumentParser()
    parser.add_argument("--load", choices=["exchanges", "all"], help="Load data from CSV")
    args = parser.parse_args()

    # Data files are always in /data directory (mapped from a:/data)
    exchanges_file = "/data/usa_exchanges.csv"

    if args.load == "exchanges":
        print(f"Loading exchanges from: {exchanges_file}")
        if os.path.exists(exchanges_file):
            success = load_exchanges(exchanges_file)

    elif args.load == "all":
        print("Loading all data from /data directory...")
        success = load_exchanges(exchanges_file)
