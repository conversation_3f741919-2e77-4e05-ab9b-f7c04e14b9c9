#!/bin/bash

# Script to run the Python application from WSL/Linux subsystem
# This script handles the proper path mapping for WSL

echo "🚀 Starting Docker containers from WSL..."

# Check if Dock<PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker Desktop."
    exit 1
fi

# Check if data directory exists (using Windows path format for WSL)
if ! docker run --rm -v "a:/data:/data" alpine:latest test -d /data; then
    echo "❌ Data directory a:/data not found or not accessible."
    echo "   Make sure your a:/data directory exists on Windows."
    exit 1
fi

# Check if usa_exchanges.csv exists
if ! docker run --rm -v "a:/data:/data" alpine:latest test -f /data/usa_exchanges.csv; then
    echo "❌ File a:/data/usa_exchanges.csv not found."
    echo "   Available files in a:/data:"
    docker run --rm -v "a:/data:/data" alpine:latest ls -la /data/ 2>/dev/null || echo "   Directory not accessible"
    exit 1
fi

echo "✅ Data file found: a:/data/usa_exchanges.csv"

# Build and run the application
echo "🔨 Building Docker containers..."
docker-compose build

if [ $? -ne 0 ]; then
    echo "❌ Docker build failed"
    exit 1
fi

echo "🏃 Running the application..."
docker-compose up python-app

echo "✅ Application finished"
